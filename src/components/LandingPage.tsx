import { motion } from 'framer-motion';
import { Play, Download, Globe, Zap, Shield, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import HowToUse from './HowToUse';
import FAQ from './FAQ';

interface LandingPageProps {
  onGetStarted: () => void;
}

const LandingPage = ({ onGetStarted }: LandingPageProps) => {
  const features = [
    {
      icon: <Play className="w-8 h-8" />,
      title: "YouTube Video Support",
      description: "Download subtitles from any YouTube video with available captions or auto-generated transcripts."
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "70+ Languages Supported",
      description: "Extract YouTube captions in multiple languages including English, Spanish, French, German, and more."
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Auto-Generated Captions",
      description: "Advanced processing of YouTube's AI-generated subtitles with automatic cleaning and formatting."
    },
    {
      icon: <Download className="w-8 h-8" />,
      title: "VTT & TXT Formats",
      description: "Download YouTube subtitles in VTT format for video players and web, or TXT with metadata for reading."
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Privacy Protected",
      description: "No data stored on our servers. YouTube subtitle extraction happens in real-time without tracking."
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Free YouTube Tool",
      description: "Completely free YouTube subtitle downloader with no registration required during beta period."
    }
  ];

  const useCases = [
    {
      title: "Content Creators",
      description: "Extract subtitles for video editing, translation, or accessibility compliance.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      title: "Students & Researchers",
      description: "Get transcripts from educational videos for note-taking and research.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      title: "Language Learners",
      description: "Study foreign languages with accurate subtitles and translations.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      title: "Accessibility Teams",
      description: "Create accessible content with properly formatted subtitle files.",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-800/20 to-pink-800/20" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl md:text-7xl font-bold text-white mb-6"
            >
              Download YouTube
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {" "}Subtitles
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto"
            >
              Free YouTube subtitle downloader and transcript extractor. Get YT captions in VTT and TXT formats.
              Export YouTube transcripts from auto-generated and manual subtitles in 70+ languages.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button
                onClick={onGetStarted}
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Play className="w-5 h-5 mr-2" />
                Get Started Free
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                className="border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-4 text-lg font-semibold rounded-full transition-all duration-300"
                onClick={() => document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Learn More
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-slate-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Powerful Features
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Everything you need to extract and process YouTube subtitles professionally
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                className="bg-slate-800/80 backdrop-blur-sm rounded-xl p-6 border border-slate-700 hover:border-purple-500 transition-all duration-300"
              >
                <div className="text-purple-400 mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Perfect For Everyone
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              From content creators to researchers, our tool serves diverse needs
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative overflow-hidden rounded-xl bg-slate-800/80 backdrop-blur-sm border border-slate-700 p-8 hover:border-purple-500 transition-all duration-300"
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${useCase.gradient} opacity-10`} />
                <div className="relative">
                  <h3 className="text-2xl font-bold text-white mb-4">
                    {useCase.title}
                  </h3>
                  <p className="text-gray-300 text-lg">
                    {useCase.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <HowToUse />

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-slate-800/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Everything you need to know about downloading YouTube subtitles
            </p>
          </motion.div>
          <FAQ showHeader={false} />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-800/20 to-pink-800/20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Extract Subtitles?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Start extracting professional-quality subtitles in seconds
            </p>
            <Button
              onClick={onGetStarted}
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-12 py-4 text-xl font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Download className="w-6 h-6 mr-2" />
              Start Extracting Now
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
