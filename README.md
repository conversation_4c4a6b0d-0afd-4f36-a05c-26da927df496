# YouTube Subtitle Extractor

A full-stack application for extracting subtitles from YouTube videos. Built with React, TypeScript, Vite, and Express.js.

## Features

- Extract subtitles from any YouTube video
- Support for multiple languages including auto-generated captions
- Real-time subtitle parsing and display
- Download subtitles in various formats
- Clean, responsive UI with Tailwind CSS
- Full TypeScript support

## Tech Stack

**Frontend:**
- React 18 with TypeScript
- Vite for fast development
- Tailwind CSS for styling
- Radix UI components
- React Router for navigation

**Backend:**
- Express.js with TypeScript (Local Development)
- Netlify Functions with TypeScript (Production)
- youtube-dl-exec for YouTube data extraction
- CORS enabled for cross-origin requests

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd YTSubtitleExtractor
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server (runs both frontend and backend):
```bash
npm run dev
```

This will start:
- Backend server on `http://localhost:3001`
- Frontend development server on `http://localhost:5173` (or next available port)

### Available Scripts

**Local Development:**
- `npm run dev:local` - Start both Express server and Vite frontend
- `npm run server` - Start only the Express backend server
- `npm run client` - Start only the Vite frontend development server

**Netlify Development:**
- `npm run dev` - Start Netlify development environment (with serverless functions)

**Production:**
- `npm run build` - Build the frontend for production
- `npm run deploy` - Deploy to Netlify
- `npm run preview` - Preview the production build

## Usage

1. Open the application in your browser
2. Paste a YouTube video URL in the input field
3. Click "Extract Subtitles" to fetch available subtitle languages
4. Select your preferred language from the dropdown
5. View and download the extracted subtitles

## Deployment

### Netlify Serverless Functions

This project supports deployment to Netlify with serverless functions:

1. **Netlify Functions** (Production):
   - `/.netlify/functions/health` - Health check endpoint
   - `/.netlify/functions/subtitles` - Handles both language listing and subtitle download

2. **Express Server** (Local Development):
   - `GET /api/health` - Health check endpoint
   - `GET /api/subtitles/languages/:videoId` - Get available subtitle languages for a video
   - `GET /api/subtitles/download/:videoId/:langCode` - Download subtitles for a specific language

### Deployment Steps

1. **Connect to Netlify:**
   ```bash
   npx netlify login
   npx netlify init
   ```

2. **Deploy:**
   ```bash
   npm run deploy
   ```

3. **Environment Variables:**
   Set any required environment variables in the Netlify dashboard.

## Project Structure

```
├── src/                    # Frontend source code
│   ├── components/         # React components
│   ├── lib/               # Utility functions
│   └── types/             # TypeScript type definitions
├── server/                # Express server (local development)
│   └── index.ts           # Express server
├── netlify/               # Netlify serverless functions
│   └── functions/         # Serverless function handlers
│       ├── utils/         # Shared utilities
│       ├── health.ts      # Health check function
│       └── subtitles.ts   # Subtitle extraction function
├── public/                # Static assets
├── netlify.toml           # Netlify configuration
└── package.json           # Dependencies and scripts
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
