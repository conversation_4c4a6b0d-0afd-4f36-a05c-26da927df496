{"name": "starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"vite\"", "server": "tsx watch server/index.ts", "client": "vite", "build-no-errors": "tsc && vite build", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr server/index.ts --outDir dist/server", "build": "npm run build:client && npm run build:server", "serve": "node server-prod.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "node dist/server/index.js", "vercel-build": "npm run build", "postinstall": "echo 'Installation complete'", "health": "node health-check.js", "validate:vercel": "node validate-vercel.js", "docker:build": "docker build -t youtube-subtitle-extractor .", "docker:run": "docker run -p 3000:3000 youtube-subtitle-extractor", "types:supabase": "npx supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > src/types/supabase.ts"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.45.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@vitejs/plugin-react": "^4.5.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^9.1.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.5", "express": "^5.1.0", "framer-motion": "^11.18.2", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.394.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.0.19", "react-router": "^6.23.1", "react-router-dom": "^6.23.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.4", "vaul": "^0.9.1", "vite": "^6.3.5", "youtube-dl-exec": "^3.0.22", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.2", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "3.4.1", "tempo-devtools": "^2.0.106", "typescript": "^5.8.2"}}