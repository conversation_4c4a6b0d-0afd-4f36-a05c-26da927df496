{"version": 2, "name": "youtube-subtitle-extractor", "builds": [{"src": "dist/server/index.js", "use": "@vercel/node", "config": {"includeFiles": ["dist/client/**", "node_modules/youtube-dl-exec/**"], "excludeFiles": ["src/**", "server/**", "public/**", "*.md"], "maxDuration": 30, "memory": 1024, "runtime": "nodejs20.x"}}], "rewrites": [{"source": "/api/(.*)", "destination": "/dist/server/index.js"}, {"source": "/(.*)", "destination": "/dist/server/index.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"NODE_ENV": "production", "PORT": "3000"}, "installCommand": "npm ci", "buildCommand": "npm run build", "outputDirectory": "dist", "framework": null, "regions": ["iad1"], "github": {"silent": true}}