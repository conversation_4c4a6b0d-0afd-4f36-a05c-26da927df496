{"version": 2, "name": "youtube-subtitle-extractor", "builds": [{"src": "dist/server/index.js", "use": "@vercel/node", "config": {"includeFiles": ["dist/client/**", "node_modules/youtube-dl-exec/**"], "excludeFiles": ["src/**", "server/**", "public/**", "*.md"]}}], "routes": [{"src": "/api/(.*)", "dest": "/dist/server/index.js"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}, "dest": "/dist/server/index.js"}, {"src": "/(.*)", "dest": "/dist/server/index.js"}], "functions": {"dist/server/index.js": {"maxDuration": 30, "memory": 1024, "runtime": "nodejs20.x"}}, "env": {"NODE_ENV": "production", "PORT": "3000"}, "installCommand": "npm ci --only=production", "buildCommand": "npm run build", "outputDirectory": "dist", "framework": null, "regions": ["iad1"], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/dist/server/index.js"}], "github": {"silent": true}}