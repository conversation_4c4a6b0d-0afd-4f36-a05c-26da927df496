{"version": 2, "name": "youtube-subtitle-extractor", "functions": {"api/**/*.js": {"maxDuration": 30, "memory": 1024, "runtime": "nodejs20.x"}}, "rewrites": [{"source": "/((?!api).*)", "destination": "/dist/client/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/dist/client/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"NODE_ENV": "production"}, "buildCommand": "npm run build", "installCommand": "npm ci"}