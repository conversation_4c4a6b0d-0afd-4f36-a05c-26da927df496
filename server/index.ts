import express, { Request, Response } from 'express';
import cors from 'cors';
import { getSubtitles } from '@treeee/youtube-caption-extractor';
import path from 'path';
import {dirname} from 'path';
import { fileURLToPath } from 'url';


const app = express();
const PORT = process.env.PORT || 3001;
const __dirname = dirname(fileURLToPath(import.meta.url));
const clientPath = path.join(__dirname, '../client');

// Middleware
app.use(cors());
app.use(express.json());

// Types



interface LanguageOption {
  code: string;
  name: string;
  isAutoGenerated?: boolean;
}

interface Subtitle {
  start: number;
  end: number;
  text: string;
}









// API Routes



// Helper function to get available languages for a video (English only)
const getAvailableLanguages = async (videoId: string): Promise<LanguageOption[]> => {
  const availableLanguages: LanguageOption[] = [];

  console.log(`🔍 Checking English subtitles for video: ${videoId}`);

  // Try multiple times with slight delay as the API might be inconsistent
  let attempts = 0;
  const maxAttempts = 3;
  let subtitles: any[] | null = null;

  while (attempts < maxAttempts && (!subtitles || subtitles.length === 0)) {
    try {
      if (attempts > 0) {
        console.log(`🔄 Retry attempt ${attempts} for English subtitles`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between retries
      }
      subtitles = await getSubtitles({ videoID: videoId, lang: 'en' });
      attempts++;
    } catch (error) {
      attempts++;
      if (attempts >= maxAttempts) {
        console.log(`❌ Error checking English subtitles after ${maxAttempts} attempts: ${error}`);
        break;
      }
    }
  }

  if (subtitles && subtitles.length > 0) {
    availableLanguages.push({
      code: 'en',
      name: 'English',
      isAutoGenerated: true // We'll assume auto-generated for now
    });
    console.log(`✅ Found English subtitles with ${subtitles.length} segments`);
  } else {
    console.log(`❌ No English subtitles found for video: ${videoId} after ${maxAttempts} attempts`);
  }

  return availableLanguages;
};

// Get available subtitle languages for a video
app.get('/api/subtitles/languages/:videoId', async (req: Request, res: Response) => {
  try {
    const { videoId } = req.params;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get available languages using the new caption extractor
    const availableLanguages = await getAvailableLanguages(videoId);

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Get video title and thumbnail
    let videoTitle = 'YouTube Video';
    let thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault for better compatibility

    try {
      // Get the title from the YouTube page directly
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
      const html = await response.text();
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        videoTitle = titleMatch[1].replace(' - YouTube', '').trim();
      }
    } catch (titleError) {
      console.log('Could not fetch video title, using default');
    }

    res.json({
      videoId,
      title: videoTitle,
      thumbnail: thumbnailUrl,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Download subtitles for a specific language
app.get('/api/subtitles/download/:videoId/:langCode', async (req: Request, res: Response) => {
  try {
    const { videoId, langCode } = req.params;

    if (!videoId || !langCode) {
      return res.status(400).json({ error: 'Video ID and language code are required' });
    }

    // Only support English subtitles
    if (langCode !== 'en') {
      return res.status(400).json({ error: 'Only English subtitles are supported' });
    }

    console.log(`Downloading English subtitles for video: ${videoId}`);

    // Get subtitles using the new caption extractor with retry mechanism
    console.log(`Downloading subtitles for video: ${videoId}, language: ${langCode}`);

    // Try multiple times with slight delay as the API might be inconsistent
    let rawSubtitles: any[] | null = null;
    let attempts = 0;
    const maxAttempts = 3;

    while (attempts < maxAttempts && (!rawSubtitles || rawSubtitles.length === 0)) {
      try {
        if (attempts > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between retries
        }
        rawSubtitles = await getSubtitles({ videoID: videoId, lang: langCode });
        attempts++;
      } catch (error) {
        attempts++;
        if (attempts >= maxAttempts) {
          throw error;
        }
      }
    }

    if (!rawSubtitles || rawSubtitles.length === 0) {
      return res.status(404).json({ error: 'Subtitles not found for the specified language' });
    }

    // Convert the subtitle format to match our expected format
    const subtitles = rawSubtitles.map((sub: any) => ({
      start: parseFloat(sub.start),
      end: parseFloat(sub.start) + parseFloat(sub.dur),
      text: sub.text
    }));

    // Get video title and thumbnail
    let videoTitle = 'YouTube Video';
    let thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault for better compatibility

    try {
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
      const html = await response.text();
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        videoTitle = titleMatch[1].replace(' - YouTube', '').trim();
      }
    } catch (titleError) {
      console.log('Could not fetch video title, using default');
    }

    res.json({
      videoId,
      title: videoTitle,
      thumbnail: thumbnailUrl,
      language: 'English',
      subtitles
    });

  } catch (error) {
    console.error('Error downloading subtitles:', error);
    res.status(500).json({
      error: 'Failed to download subtitles',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/api/health', (_req: Request, res: Response) => {
  res.json({ status: 'OK', message: 'YouTube Subtitle Extractor API is running' });
});

// API 404 handler - for any unmatched API routes
app.all('/api/*splat', (req: Request, res: Response) => {
  res.status(404).json({ 
    error: 'API endpoint not found',
    path: req.path
  });
});

// ✅ Serve static files (built by Vite) in production
if (process.env.NODE_ENV === 'production') {
  console.log(`Serving static files from ${clientPath}`);
  app.use(express.static(clientPath));
  
  // For any other routes, serve the index.html to let React Router handle it
  app.get('/', (_req, res) => {
    res.sendFile(path.join(clientPath, 'index.html'));
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
