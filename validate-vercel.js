#!/usr/bin/env node

/**
 * Validate vercel.json configuration
 * Checks for common configuration conflicts and issues
 */

import { readFileSync } from 'fs';
import { join } from 'path';

try {
  console.log('🔍 Validating vercel.json configuration...');
  
  const vercelConfig = JSON.parse(readFileSync(join(process.cwd(), 'vercel.json'), 'utf8'));
  
  // Check for conflicting properties
  const conflicts = [];
  
  if (vercelConfig.functions && vercelConfig.builds) {
    conflicts.push('❌ Cannot use both "functions" and "builds" properties');
  }
  
  if (vercelConfig.routes && (vercelConfig.rewrites || vercelConfig.redirects || vercelConfig.headers || vercelConfig.cleanUrls || vercelConfig.trailingSlash)) {
    conflicts.push('❌ Cannot use "routes" with "rewrites", "redirects", "headers", "cleanUrls", or "trailingSlash"');
  }
  
  if (conflicts.length > 0) {
    console.log('\n🚨 Configuration conflicts found:');
    conflicts.forEach(conflict => console.log(conflict));
    process.exit(1);
  }
  
  // Validate structure
  console.log('✅ No configuration conflicts found');
  
  if (vercelConfig.builds) {
    console.log(`📦 Found ${vercelConfig.builds.length} build(s)`);
  }
  
  if (vercelConfig.rewrites) {
    console.log(`🔄 Found ${vercelConfig.rewrites.length} rewrite rule(s)`);
  }
  
  if (vercelConfig.headers) {
    console.log(`📋 Found ${vercelConfig.headers.length} header rule(s)`);
  }
  
  console.log('✅ vercel.json configuration is valid!');
  
} catch (error) {
  console.error('❌ Error validating vercel.json:', error.message);
  process.exit(1);
}
