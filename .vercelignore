# Development files
src/
server/
public/
*.md
*.log

# Build tools
vite.config.ts
tsconfig.json
tsconfig.node.json
tailwind.config.js
postcss.config.js
components.json
tempo.config.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Development dependencies (keep only production dependencies)
# Note: Vercel will install from package.json anyway

# Keep these for deployment:
# dist/
# package.json
# package-lock.json
# vercel.json
# server-prod.js
